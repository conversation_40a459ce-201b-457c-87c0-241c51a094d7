import React from 'react';
import { FaArrowLeft } from 'react-icons/fa';

const FixedBackButton = ({ onClick, text = "Back to Dashboard", className = "" }) => {
  return (
    <div className="fixed-back-button-container">
      <button 
        onClick={onClick} 
        className={`fixed-back-button ${className}`}
        aria-label={text}
      >
        <FaArrowLeft /> {text}
      </button>
    </div>
  );
};

export default FixedBackButton;
